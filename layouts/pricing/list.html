{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<!-- Load reCAPTCHA script -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<section class="section is-medium pricing-page">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet  has-text-centered">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.Subtitle }}</h5>
        <div class="divider is-centered"></div>
      </div>
    </div>

    <div class="content">
      <div class="columns">
        <div class="column is-8 is-offset-2">
          <div class="content">
            <p>We believe cloud pricing should be as flexible as the infrastructure itself.</p>
            <p>Rather than fixed plans, we offer custom quotes based on your specific needs — whether you're running a few virtual machines or building a sovereign, multi-tenant cloud.</p>
            <p>Tell us how much capacity you need below, and we'll provide a tailored proposal.</p>
          </div>

          <div class="quote-form">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="form-message success"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-success').style.display='none'"
              >&times;</button>
              <strong>Thank you!</strong> Your quote request has been submitted successfully.
              We'll get back to you within 1 business day.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="form-message error"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-error').style.display='none'"
              >&times;</button>
              There was a problem submitting your quote request. Please try again.
            </div>

            <h3 class="form-title">Quote Request Form</h3>

            <form
              id="pricing-form"
              action="{{ .Site.Params.section5.action }}"
              method="POST"
            >
              <div class="columns is-multiline">
                <!-- Name and Email Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Your Name</label>
                    <input
                      class="input"
                      name="name"
                      type="text"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Email Address</label>
                    <input
                      class="input"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>




                <div class="column is-6">
                  <div class="field">
                    <label class="label">Your Name</label>
                    <input
                      class="input"
                      name="name"
                      type="text"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Email Address</label>
                    <input
                      class="input"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>


                <!-- Technical Requirements -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Number of vCPUs</label>
                    <input
                      class="input"
                      id="vcpus"
                      name="vcpus"
                      type="number"
                      min="1"
                      placeholder="e.g., 16"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Memory (in GB)</label>
                    <input
                      class="input"
                      id="memory"
                      name="memory"
                      type="number"
                      min="1"
                      placeholder="e.g., 64"
                      required
                    />
                  </div>
                </div>

                <div class="column is-6">
                  <div class="field">
                    <label class="label">Block Storage (in TB)</label>
                    <input
                      class="input"
                      id="blockStorage"
                      name="blockStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 2"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Object Storage (in TB)</label>
                    <input
                      class="input"
                      id="objectStorage"
                      name="objectStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 10"
                    />
                  </div>
                </div>

                <!-- Service Model -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Service Model</label>
                    <div class="select is-fullwidth">
                      <select id="serviceModel" name="serviceModel" required>
                        <option value="">-- Select a model --</option>
                        <option value="self-managed">Self Managed Cloud</option>
                        <option value="caas">Cloud as a Service</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- Hardware Quote Checkbox -->
                <div class="column is-12">
                  <div class="checkbox-field">
                    <input type="checkbox" id="hardwareQuote" name="hardwareQuote">
                    <label for="hardwareQuote">I would like to receive a hardware quote as well</label>
                  </div>
                  <p class="form-note">* Hardware quotes are delivered by a trusted partner of whitesky.cloud, not by whitesky.cloud itself.</p>
                </div>

                <!-- Additional Information -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Additional Requests or Information</label>
                    <textarea
                      class="textarea"
                      id="additionalInfo"
                      name="additionalInfo"
                      rows="4"
                      placeholder="e.g., Need GPU support (NVIDIA A100), or Database as a Service..."
                    ></textarea>
                  </div>
                </div>

                <!-- Hidden fields for Formspree -->
                <input type="hidden" name="subject" value="Quote Request" />
                <textarea name="message" style="display: none;" readonly></textarea>
                <!-- Formspree honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />

                <div class="column is-12">
                  <button
                    id="submit-button"
                    class="submit-button button cta is-large primary-btn raised is-clear g-recaptcha"
                    data-sitekey="{{ .Site.Params.section5.recaptcha_site_key }}"
                    data-callback="onSubmit"
                    type="button"
                  >
                    Submit Quote Request
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- How It Works Section -->
          <div class="info-section">
            <h2>How It Works</h2>
            <ul>
              <li>You fill in your capacity needs</li>
              <li>We calculate a tailored, no-obligation quote</li>
              <li>We'll reach out personally within 1 business day</li>
            </ul>

            <h3>Why We Do It This Way</h3>
            <p>Our platform supports private, public, and hybrid cloud models — and we want to make sure you get the best setup at the right price. By understanding your requirements, we can recommend the ideal combination of performance, resilience, and cost.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  var form, submitButton, successMessage, errorMessage

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("pricing-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");
  });

  function buildMessageFromFormData() {
    // Collect all form data except name, email, subject, message, and hidden fields
    var vcpus = document.getElementById("vcpus").value;
    var memory = document.getElementById("memory").value;
    var blockStorage = document.getElementById("blockStorage").value;
    var objectStorage = document.getElementById("objectStorage").value;
    var serviceModel = document.getElementById("serviceModel").value;
    var hardwareQuote = document.getElementById("hardwareQuote").checked;
    var additionalInfo = document.getElementById("additionalInfo").value;

    var message = "QUOTE REQUEST DETAILS:\n\n";
    message += "Technical Requirements:\n";
    message += "- vCPUs: " + (vcpus || "Not specified") + "\n";
    message += "- Memory: " + (memory || "Not specified") + " GB\n";
    message += "- Block Storage: " + (blockStorage || "0") + " TB\n";
    message += "- Object Storage: " + (objectStorage || "0") + " TB\n";
    message += "- Service Model: " + (serviceModel || "Not specified") + "\n";
    message += "- Hardware Quote Requested: " + (hardwareQuote ? "Yes" : "No") + "\n";

    if (additionalInfo) {
      message += "\nAdditional Information:\n" + additionalInfo;
    }

    return message;
  }

  async function submitForm() {
    // Hide any previous messages
    successMessage.style.display = "none";
    errorMessage.style.display = "none";

    // Change button text to show loading state
    var originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    // Build the message from form data
    var messageField = document.querySelector('textarea[name="message"]');
    messageField.value = buildMessageFromFormData();

    // Create form data from the form element
    var data = new FormData(form);

    try {
      const response = await fetch(form.action, {
        method: form.method,
        body: data,
        headers: {
          Accept: "application/json",
        },
      });

      if (response.ok) {
        successMessage.style.display = "block";
        form.reset();
        // Scroll to success message
        successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        errorMessage.style.display = "block";
        errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } catch (error) {
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    submitButton.textContent = originalButtonText;
    submitButton.disabled = false;

    // Re-enable reCAPTCHA functionality by resetting the widget
    if (typeof grecaptcha !== "undefined") {
      grecaptcha.reset();
    }
  }

  // Global function for reCAPTCHA callback (required by Formspree)
  function onSubmit(token) {
    // Validate form before submitting
    if (!form.checkValidity()) {
      form.reportValidity();
      // Reset reCAPTCHA if validation fails so user can try again
      if (typeof grecaptcha !== "undefined") {
        grecaptcha.reset();
      }
      return;
    }

    // Submit the form - the reCAPTCHA token is automatically included by the reCAPTCHA library
    submitForm();
  }
</script>

{{ partial "footer.html" . }}

{{ end }}
